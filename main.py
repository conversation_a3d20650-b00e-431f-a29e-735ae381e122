# study_manager_backend.py
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import requests
import base64
import os
import json
from datetime import date
from PIL import Image
import io
import tempfile
from werkzeug.utils import secure_filename

app = Flask(__name__)
CORS(app)

# 从环境变量读取配置
CONFIG = {
    'USERNAME': os.getenv('STUDY_USERNAME', 'jhxxxr'),
    'PASSWORD': os.getenv('STUDY_PASSWORD', 'jhx666666'),
    'NOTION_TOKEN': os.getenv('NOTION_TOKEN', '**************************************************'),
    'DATABASE_ID': os.getenv('DATABASE_ID', '24c21bb96eca810f8a6adbcd4c336ae8'),
    'OVERVIEW_PAGE_ID': os.getenv('OVERVIEW_PAGE_ID', ''),  # 总览页面ID，需要设置
    'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY', 'fo-ZNXFoYAVdkctnUw4GVp0k0stay9VIFzE'),
    'OPENAI_BASE_URL': os.getenv('OPENAI_BASE_URL', 'https://v2.voct.top/v1'),
    'MODEL_NAME': os.getenv('MODEL_NAME', 'gpt-4o-mini'),
    'UPLOAD_FOLDER': os.getenv('UPLOAD_FOLDER', './uploads'),
    'MAX_CONTENT_LENGTH': 16 * 1024 * 1024  # 16MB
}

# 创建上传文件夹
os.makedirs(CONFIG['UPLOAD_FOLDER'], exist_ok=True)
app.config['MAX_CONTENT_LENGTH'] = CONFIG['MAX_CONTENT_LENGTH']

# 允许的文件类型
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def call_openai_api(prompt, image_base64=None):
    """调用OpenAI API"""
    url = f"{CONFIG['OPENAI_BASE_URL']}/chat/completions"
    headers = {
        'Authorization': f"Bearer {CONFIG['OPENAI_API_KEY']}",
        'Content-Type': 'application/json'
    }
    
    # 构建消息
    if image_base64:
        messages = [{
            'role': 'user',
            'content': [
                {'type': 'text', 'text': prompt},
                {'type': 'image_url', 'image_url': {'url': f'data:image/jpeg;base64,{image_base64}'}}
            ]
        }]
    else:
        messages = [{'role': 'user', 'content': prompt}]
    
    # 尝试不同的请求格式
    payloads = [
        {
            'model': CONFIG['MODEL_NAME'],
            'messages': messages,
            'max_tokens': 500,
            'temperature': 0.3
        },
        {
            'model': CONFIG['MODEL_NAME'],
            'messages': messages,
            'max_tokens': 500
        },
        {
            'model': CONFIG['MODEL_NAME'],
            'messages': messages
        }
    ]
    
    for payload in payloads:
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            elif response.status_code != 400:
                break
        except Exception as e:
            print(f"API调用异常: {e}")
            continue
    
    raise Exception(f"OpenAI API调用失败: {response.status_code}")

def get_today_notion_record():
    """获取今天的Notion记录"""
    url = f"https://api.notion.com/v1/databases/{CONFIG['DATABASE_ID']}/query"
    headers = {
        'Authorization': f"Bearer {CONFIG['NOTION_TOKEN']}",
        'Content-Type': 'application/json',
        'Notion-Version': '2022-06-28'
    }
    
    query = {
        'filter': {
            'property': '日期',
            'date': {'equals': str(date.today())}
        }
    }
    
    response = requests.post(url, json=query, headers=headers)
    if response.status_code != 200:
        raise Exception(f"Notion查询失败: {response.status_code}")
    
    data = response.json()
    return data['results'][0] if data['results'] else None

def update_notion_page(page_id, properties):
    """更新Notion页面"""
    url = f"https://api.notion.com/v1/pages/{page_id}"
    headers = {
        'Authorization': f"Bearer {CONFIG['NOTION_TOKEN']}",
        'Content-Type': 'application/json',
        'Notion-Version': '2022-06-28'
    }
    
    data = {'properties': properties}
    response = requests.patch(url, json=data, headers=headers)
    
    if response.status_code != 200:
        raise Exception(f"Notion更新失败: {response.status_code}")
    
    return response.json()

def get_subject_content(record, subject):
    """获取科目内容"""
    if not record or 'properties' not in record:
        return ''
    
    prop = record['properties'].get(subject, {})
    if prop.get('rich_text') and len(prop['rich_text']) > 0:
        return prop['rich_text'][0]['plain_text']
    return ''

def add_image_to_notion_page(page_id, image_url, description=""):
    """向Notion页面添加图片"""
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    headers = {
        'Authorization': f"Bearer {CONFIG['NOTION_TOKEN']}",
        'Content-Type': 'application/json',
        'Notion-Version': '2022-06-28'
    }
    
    # 添加图片块
    children = [
        {
            "object": "block",
            "type": "image",
            "image": {
                "type": "external",
                "external": {"url": image_url}
            }
        }
    ]
    
    if description:
        children.insert(0, {
            "object": "block",
            "type": "paragraph",
            "paragraph": {
                "rich_text": [{"type": "text", "text": {"content": description}}]
            }
        })
    
    data = {"children": children}
    response = requests.patch(url, json=data, headers=headers)
    
    return response.status_code == 200

# API路由
@app.route('/')
def index():
    """返回主页面"""
    with open('study_manager.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/api/login', methods=['POST'])
def login():
    """用户登录验证"""
    data = request.json
    username = data.get('username')
    password = data.get('password')
    
    if username == CONFIG['USERNAME'] and password == CONFIG['PASSWORD']:
        return jsonify({'success': True, 'message': '登录成功'})
    else:
        return jsonify({'success': False, 'message': '用户名或密码错误'}), 401

@app.route('/api/upload-image', methods=['POST'])
def upload_image():
    """上传并分析图片"""
    try:
        if 'image' not in request.files:
            return jsonify({'success': False, 'message': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'message': '不支持的文件类型'}), 400
        
        # 保存文件
        filename = secure_filename(f"{date.today()}_{file.filename}")
        filepath = os.path.join(CONFIG['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # 转换为base64
        with open(filepath, 'rb') as img_file:
            image_data = base64.b64encode(img_file.read()).decode('utf-8')
        
        # AI分析图片
        analysis_prompt = """
请分析这张学习图片，识别出学习的科目和具体内容。
请按照以下格式返回JSON（不要包含其他文字）：
{
    "subject": "数学|英语|政治|计算机408",
    "content": "具体学习内容描述",
    "confidence": "高|中|低"
}

如果无法确定科目，请返回最可能的科目。
"""
        
        analysis_result = call_openai_api(analysis_prompt, image_data)
        
        try:
            analysis_data = json.loads(analysis_result)
        except json.JSONDecodeError:
            # 如果返回的不是标准JSON，尝试提取关键信息
            analysis_data = {
                "subject": "计算机408",  # 默认科目
                "content": analysis_result,
                "confidence": "中"
            }
        
        # 更新Notion记录
        record = get_today_notion_record()
        if record:
            # 更新科目内容
            properties = {
                analysis_data['subject']: {
                    'rich_text': [{
                        'text': {'content': analysis_data['content']}
                    }]
                }
            }
            
            update_notion_page(record['id'], properties)
            
            # 如果有总览页面ID，添加图片
            if CONFIG['OVERVIEW_PAGE_ID']:
                # 这里需要将图片上传到可访问的URL，比如图床服务
                # 暂时返回本地路径，实际使用时需要配置图床
                image_url = f"http://localhost:5000/uploads/{filename}"
                description = f"【{analysis_data['subject']}】{analysis_data['content']}"
                add_image_to_notion_page(CONFIG['OVERVIEW_PAGE_ID'], image_url, description)
        
        # 删除临时文件
        os.remove(filepath)
        
        return jsonify({
            'success': True,
            'analysis': analysis_data,
            'message': '图片分析完成并已更新记录'
        })
        
    except Exception as e:
        print(f"图片分析错误: {e}")
        return jsonify({'success': False, 'message': f'处理失败: {str(e)}'}), 500

@app.route('/api/update-study', methods=['POST'])
def update_study():
    """手动更新学习记录"""
    try:
        data = request.json
        subject = data.get('subject')
        content = data.get('content')
        merge = data.get('merge', False)
        
        if not subject or not content:
            return jsonify({'success': False, 'message': '科目和内容不能为空'}), 400
        
        record = get_today_notion_record()
        if not record:
            return jsonify({'success': False, 'message': '未找到今天的记录'}), 404
        
        final_content = content
        
        # 如果需要合并内容
        if merge:
            current_content = get_subject_content(record, subject)
            if current_content:
                merge_prompt = f"""
请将以下两段学习内容智能合并，去除重复，保持逻辑清晰：

原有内容: {current_content}
新增内容: {content}

请直接返回合并后的内容，不要添加额外说明。
"""
                final_content = call_openai_api(merge_prompt)
        
        # 更新Notion
        properties = {
            subject: {
                'rich_text': [{
                    'text': {'content': final_content}
                }]
            }
        }
        
        update_notion_page(record['id'], properties)
        
        return jsonify({
            'success': True,
            'message': '学习记录更新成功',
            'final_content': final_content
        })
        
    except Exception as e:
        print(f"更新记录错误: {e}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

@app.route('/api/today-data', methods=['GET'])
def get_today_data():
    """获取今日学习数据"""
    try:
        record = get_today_notion_record()
        if not record:
            return jsonify({'success': False, 'message': '今天还没有学习记录'})
        
        subjects = ['数学', '英语', '政治', '计算机408']
        data = {}
        
        for subject in subjects:
            data[subject] = get_subject_content(record, subject)
        
        return jsonify({
            'success': True,
            'data': data,
            'date': str(date.today())
        })
        
    except Exception as e:
        print(f"获取数据错误: {e}")
        return jsonify({'success': False, 'message': f'获取数据失败: {str(e)}'}), 500

@app.route('/api/generate-summary', methods=['POST'])
def generate_summary():
    """生成AI总结（原有功能）"""
    try:
        record = get_today_notion_record()
        if not record:
            return jsonify({'success': False, 'message': '未找到今天的记录'})
        
        # 获取各科内容
        subjects = ['数学', '英语', '政治', '计算机408']
        contents = {}
        for subject in subjects:
            contents[subject] = get_subject_content(record, subject)
        
        # 生成总结
        summary_prompt = f"""
请根据以下学习内容生成一段简洁、有条理的总结，用于当日学习复盘。语言为中文，不要使用 markdown，控制在150字以内。

数学: {contents['数学'] or '无'}
英语: {contents['英语'] or '无'}  
政治: {contents['政治'] or '无'}
计算机408: {contents['计算机408'] or '无'}
"""
        
        summary = call_openai_api(summary_prompt)
        
        # 更新总结到Notion
        properties = {
            '总结（AI生成）': {
                'rich_text': [{
                    'text': {'content': summary}
                }]
            }
        }
        
        update_notion_page(record['id'], properties)
        
        return jsonify({
            'success': True,
            'summary': summary,
            'message': '总结生成完成并已更新'
        })
        
    except Exception as e:
        print(f"生成总结错误: {e}")
        return jsonify({'success': False, 'message': f'生成总结失败: {str(e)}'}), 500

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传文件的访问"""
    from flask import send_from_directory
    return send_from_directory(CONFIG['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    print("学习管理系统后端启动中...")
    print(f"访问地址: http://localhost:5000")
    print(f"用户名: {CONFIG['USERNAME']}")
    print("=" * 50)
    
    # 检查环境变量配置
    missing_configs = []
    required_configs = ['NOTION_TOKEN', 'DATABASE_ID', 'OPENAI_API_KEY']
    
    for config in required_configs:
        if not CONFIG.get(config) or CONFIG[config].startswith('your_'):
            missing_configs.append(config)
    
    if missing_configs:
        print("⚠️  警告：以下配置项可能需要设置环境变量：")
        for config in missing_configs:
            print(f"   export {config}=your_actual_value")
        print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)