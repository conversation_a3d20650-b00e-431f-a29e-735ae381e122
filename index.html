<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
            width: 100%;
            max-width: 1000px;
            min-height: 600px;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .logout-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .login-form, .main-content {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-success {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .tab.active {
            background: white;
            color: #4facfe;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .upload-area {
            border: 2px dashed #4facfe;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            background: rgba(79, 172, 254, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-area:hover {
            background: rgba(79, 172, 254, 0.1);
            transform: scale(1.02);
        }

        .upload-area.dragover {
            background: rgba(79, 172, 254, 0.2);
            border-color: #667eea;
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .progress-container {
            margin: 1rem 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e1e1;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
            width: 0%;
        }

        .status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: 600;
            display: none;
        }

        .status.show {
            display: block;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 1rem 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }

        .subject-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .subject-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .subject-card:hover {
            border-color: #4facfe;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .subject-card.selected {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
        }

        .subject-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .subject-card p {
            color: #666;
            font-size: 0.9rem;
        }

        .data-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1rem;
            border-left: 4px solid #4facfe;
        }

        .data-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-card .content {
            color: #666;
            line-height: 1.6;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                width: 100%;
                margin: 0;
                min-height: 100vh;
                border-radius: 0;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .subject-grid {
                grid-template-columns: 1fr;
            }

            .upload-area {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录界面 -->
        <div id="loginPage">
            <div class="header">
                <h1>🎓 学习管理系统</h1>
                <p>智能化学习进度追踪与分析</p>
            </div>
            <div class="login-form">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required placeholder="请输入用户名" autocomplete="username">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required placeholder="请输入密码" autocomplete="current-password">
                    </div>
                    <button type="submit" class="btn" style="width: 100%;">
                        <span id="loginBtnText">🔑 登录</span>
                        <div class="loading hidden" id="loginLoading"></div>
                    </button>
                </form>
                <div id="loginStatus" class="status"></div>
            </div>
        </div>

        <!-- 主界面 -->
        <div id="mainPage" class="hidden">
            <div class="header" style="position: relative;">
                <h1>🎓 学习管理系统</h1>
                <p id="dateDisplay"></p>
                <button class="logout-btn" onclick="logout()">🚪 退出登录</button>
            </div>

            <div class="main-content">
                <div class="tabs">
                    <div class="tab active" onclick="showTab('upload')">📸 图片分析</div>
                    <div class="tab" onclick="showTab('manual')">✏️ 手动录入</div>
                    <div class="tab" onclick="showTab('view')">📊 数据查看</div>
                    <div class="tab" onclick="showTab('summary')">🤖 AI总结</div>
                </div>

                <!-- 图片上传分析 -->
                <div id="uploadTab" class="tab-content active">
                    <div class="upload-area" onclick="document.getElementById('imageInput').click()" 
                         ondragover="handleDragOver(event)" ondrop="handleDrop(event)">
                        <div class="upload-icon">📷</div>
                        <h3>拖拽或点击上传学习图片</h3>
                        <p>支持 JPG、PNG 等格式，AI将自动识别学习内容</p>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    </div>
                    
                    <div class="progress-container hidden" id="uploadProgressContainer">
                        <div class="progress-bar">
                            <div class="progress-fill" id="uploadProgressFill"></div>
                        </div>
                        <p style="text-align: center; margin-top: 0.5rem;">上传中...</p>
                    </div>
                    
                    <img id="imagePreview" class="image-preview">
                    <div id="uploadStatus" class="status"></div>
                    
                    <div id="analysisResult" class="hidden" style="margin-top: 1rem;">
                        <div class="data-card">
                            <h4>🤖 AI 分析结果</h4>
                            <div class="content" id="analysisContent"></div>
                        </div>
                    </div>
                </div>

                <!-- 手动输入 -->
                <div id="manualTab" class="tab-content">
                    <div class="form-group">
                        <label>选择科目</label>
                        <div class="subject-grid">
                            <div class="subject-card" onclick="selectSubject('数学')" data-subject="数学">
                                <h3><span>📐</span>数学</h3>
                                <p>高等数学、线性代数、概率统计</p>
                            </div>
                            <div class="subject-card" onclick="selectSubject('英语')" data-subject="英语">
                                <h3><span>📚</span>英语</h3>
                                <p>词汇、阅读、写作、翻译</p>
                            </div>